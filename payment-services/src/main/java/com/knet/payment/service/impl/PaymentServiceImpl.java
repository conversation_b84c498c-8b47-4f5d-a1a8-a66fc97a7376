package com.knet.payment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.dto.message.InventoryFailedMessage;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.KnetPaymentGroupStatus;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.NumberUtils;
import com.knet.common.utils.RandomStrUtil;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.req.PayPaymentRequest;
import com.knet.payment.model.dto.req.RefundPaymentRequest;
import com.knet.payment.model.dto.resp.CreatePaymentResponse;
import com.knet.payment.model.dto.resp.OrderPaymentInfoResponse;
import com.knet.payment.model.dto.resp.PayResponse;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.model.entity.SysPaymentGroup;
import com.knet.payment.model.entity.SysRefundRecord;
import com.knet.payment.service.*;
import com.knet.payment.strategy.CompensationStrategy;
import com.knet.payment.strategy.CompensationStrategyFactory;
import com.knet.payment.strategy.PaymentStrategy;
import com.knet.payment.strategy.PaymentStrategyFactory;
import com.knet.payment.system.event.PaymentEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:50
 * @description: 支付服务实现类
 */
@Slf4j
@Service
public class PaymentServiceImpl implements IPaymentService {
    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private ISysPaymentGroupService paymentGroupService;
    @Resource
    private ISysPaymentFlowService paymentFlowService;
    @Resource
    private PaymentStrategyFactory paymentStrategyFactory;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private ISysRefundRecordService sysRefundRecordService;
    @Resource
    private ISysWalletRecordService sysWalletRecordService;
    @Resource
    private IWalletOperationService walletOperationService;
    @Resource
    private CompensationStrategyFactory compensationStrategyFactory;

    /**
     * 创建支付
     *
     * @param request 创建支付请求
     * @return 创建支付响应
     */
    @DistributedLock(key = "'payment:create:'+#request.userId+':'+#request.orderId", expire = 5)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreatePaymentResponse createPayment(CreatePaymentRequest request) {
        CreatePaymentRequest.checkCreatePaymentRequest(request);
        Long userId = request.getUserId();
        String orderId = request.getOrderId();
        BigDecimal amount = request.getAmount();
        PaymentChannel paymentChannel = request.getPaymentChannel();
        log.info("创建支付开始: userId={}, orderId={}, amount={}, channel={}", userId, orderId, amount, paymentChannel.getName());
        try {
            OrderPaymentInfoResponse dbPayment = paymentGroupService.getPaymentInfoByOrderId(orderId);
            if (BeanUtil.isNotEmpty(dbPayment)) {
                throw new ServiceException("支付订单已经创建，不需要重复创建 父订单ID: " + request.getOrderId());
            }
            String groupId = randomStrUtil.getPaymentGroupId();
            String paymentId = randomStrUtil.getPaymentId();
            boolean groupSaved = paymentGroupService.createPaymentGroup(groupId, userId, orderId, amount);
            if (!groupSaved) {
                throw new ServiceException("支付组创建失败");
            }
            CreatePaymentResponse response = CreatePaymentResponse.builder()
                    .groupId(groupId)
                    .paymentId(paymentId)
                    .userId(userId)
                    .orderId(orderId)
                    .amount(NumberUtils.formatDecimal(amount))
                    .paymentChannel(paymentChannel.getName())
                    .status(KnetPaymentFlowStatus.PENDING.getName())
                    .createTime(DateUtil.now())
                    .build();
            log.info("创建支付成功: paymentId={}, groupId={}, status={}", paymentId, groupId, response.getStatus());
            return response;
        } catch (Exception e) {
            log.error("创建支付失败: userId={}, orderId={}, error={}", userId, orderId, e.getMessage(), e);
            //todo 创建支付组异常兜底处理逻辑
            throw new ServiceException("创建支付失败: " + e.getMessage());
        }
    }

    @Override
    public CreatePaymentResponse queryPaymentStatus(String paymentId) {
        log.info("查询支付状态: paymentId={}", paymentId);
        try {
            SysPaymentFlow paymentFlow = paymentFlowService.getByPaymentId(paymentId);
            if (paymentFlow == null) {
                throw new ServiceException("支付流水不存在: " + paymentId);
            }
            SysPaymentGroup paymentGroup = paymentGroupService.getByGroupId(paymentFlow.getGroupId());
            if (paymentGroup == null) {
                throw new ServiceException("支付组不存在: " + paymentFlow.getGroupId());
            }
            CreatePaymentResponse response = CreatePaymentResponse.createResponse(paymentFlow, paymentGroup);
            log.info("查询支付状态成功: paymentId={}, status={}", paymentId, paymentFlow.getStatus().getName());
            return response;
        } catch (Exception e) {
            log.error("查询支付状态失败: paymentId={}, error={}", paymentId, e.getMessage(), e);
            throw new ServiceException("查询支付状态失败: " + e.getMessage());
        }
    }

    @DistributedLock(key = "'payment:pay:'+#request.userId+':'+#request.orderId", expire = 10)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResponse payPayment(PayPaymentRequest request) {
        PayPaymentRequest.checkCreatePaymentRequest(request);
        Long userId = request.getUserId();
        String orderId = request.getOrderId();
        BigDecimal amount = request.getAmount();
        PaymentChannel paymentChannel = request.getPaymentChannel();
        log.info("用户支付开始: userId={}, orderId={}, amount={}, channel={}", userId, orderId, amount, paymentChannel.getName());
        SysPaymentGroup paymentGroup = paymentGroupService.getByGroupIdByOrderId(orderId);
        if (BeanUtil.isEmpty(paymentGroup)) {
            log.error("支付订单不存在，订单ID: {}", orderId);
            throw new ServiceException("PAYMENT-ORDER-DOES-NOT-EXIST-ORDER-ID: " + orderId);
        }
        if (KnetPaymentGroupStatus.COMPLETED.equals(paymentGroup.getStatus())) {
            log.error("订单已支付完成，无需重复支付");
            throw new ServiceException("Order has been paid, no duplicate payment is required");
        }
        if (KnetPaymentGroupStatus.CLOSED.equals(paymentGroup.getStatus())) {
            log.error("支付订单已关闭，无法支付");
            throw new ServiceException("Payment order has been closed and cannot be paid");
        }
        try {
            checkPayment(paymentGroup, orderId, amount);
            String paymentId = randomStrUtil.getPaymentId();
            SysPaymentFlow paymentFlow = SysPaymentFlow.create(paymentId, paymentGroup.getGroupId(), paymentChannel, amount);
            boolean flowSaved = paymentFlowService.save(paymentFlow);
            if (!flowSaved) {
                throw new ServiceException("支付流水创建失败");
            }
            // 执行支付处理
            PaymentStrategy strategy = paymentStrategyFactory.getStrategy(paymentChannel);
            CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder = CreatePaymentResponse.builder()
                    .groupId(paymentFlow.getGroupId())
                    .paymentId(paymentFlow.getPaymentId())
                    .userId(userId)
                    .orderId(orderId)
                    .amount(NumberUtils.formatDecimal(amount))
                    .paymentChannel(paymentChannel.getName())
                    .createTime(DateUtil.now());
            CreatePaymentRequest createRequest = CreatePaymentRequest.createRequest(userId, orderId, amount, paymentChannel);
            CreatePaymentResponse paymentResponse = strategy.processPayment(paymentFlow, createRequest, responseBuilder);
            // 发送支付结果事件（在支付处理后发送）
            PaymentEvent paymentEvent = new PaymentEvent(this, paymentGroup, "SUCCESS");
            eventPublisher.publishEvent(paymentEvent);
            PayResponse response = PayResponse.fromCreatePaymentResponse(userId, orderId, paymentResponse);
            log.info("用户支付完成: paymentId={}, status={}", paymentId, paymentResponse.getStatus());
            return response;
        } catch (Exception e) {
            log.error("用户支付失败: userId={}, orderId={}, error={}", userId, orderId, e.getMessage(), e);
            // 发送支付失败事件（可能是钱包金额不足，可能是代码存在异常）
            PaymentEvent paymentEvent = new PaymentEvent(this, paymentGroup, "FAILED");
            eventPublisher.publishEvent(paymentEvent);
            throw new ServiceException("用户支付失败: " + e.getMessage());
        }
    }

    /**
     * 处理库存扣减失败补偿
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processInventoryFailed(String messageBody) {
        InventoryFailedMessage message = JSON.parseObject(messageBody, InventoryFailedMessage.class);
        log.info("支付服务处理库存扣减失败补偿: {}", messageBody);
        try {
            String orderId = message.getOrderId();
            var paymentInfo = paymentGroupService.getPaymentInfoByOrderId(orderId);
            if (paymentInfo == null) {
                log.warn("支付组不存在，无需补偿: orderId={}", orderId);
                return;
            }
            var paymentGroup = paymentGroupService.getByGroupId(paymentInfo.getGroupId());
            if (paymentGroup != null && !KnetPaymentGroupStatus.CLOSED.equals(paymentGroup.getStatus())) {
                paymentGroup.setStatus(KnetPaymentGroupStatus.CLOSED);
                paymentGroupService.updateById(paymentGroup);
                log.info("支付组状态已更新为已关闭: groupId={}", paymentInfo.getGroupId());
            }
            log.info("库存扣减失败补偿处理完成: orderId={}, reason={}", orderId, message.getFailureReason());
        } catch (Exception e) {
            log.error("处理库存扣减失败补偿异常: orderId={}, error={}", message.getOrderId(), e.getMessage());
            throw new ServiceException("处理库存扣减失败补偿异常: " + e.getMessage());
        }
    }

    /**
     * 检查支付状态
     *
     * @param paymentGroup 支付组
     * @param orderId      订单ID
     * @param amount       支付金额
     */
    private static void checkPayment(SysPaymentGroup paymentGroup, String orderId, BigDecimal amount) {
        if (BeanUtil.isEmpty(paymentGroup)) {
            throw new ServiceException("支付订单不存在，订单ID: " + orderId);
        }
        if (amount.compareTo(new BigDecimal(String.valueOf(paymentGroup.getTotalAmount()))) != 0) {
            throw new ServiceException("支付金额与订单金额不匹配");
        }
    }

    @DistributedLock(key = "'payment:refund:'+#request.userId+':'+#request.orderItemId", expire = 10)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refundPayment(RefundPaymentRequest request) {
        log.info("退款开始: userId={}, orderItemId={}, orderItemNo={}, amount={}",
                request.getUserId(), request.getOrderItemId(), request.getOrderItemNo(), request.getAmount());
        if (sysRefundRecordService.isOrderItemRefunded(request.getOrderItemId())) {
            log.warn("订单项已退款，不能重复退款: orderItemId={}", request.getOrderItemId());
            return;
        }
        SysPaymentGroup paymentGroup = paymentGroupService.getByGroupIdByOrderId(request.getPrentOrderId());
        if (paymentGroup == null) {
            log.warn("支付订单不存在，跳过退款处理: orderId={}", request.getPrentOrderId());
            return;
        }
        if (!KnetPaymentGroupStatus.COMPLETED.equals(paymentGroup.getStatus())) {
            log.warn("订单未支付完成，跳过退款处理: orderId={}, status={}",
                    request.getPrentOrderId(), paymentGroup.getStatus());
            return;
        }
        BigDecimal totalRefundAmount = sysRefundRecordService.getTotalRefundAmountByOrderItemId(request.getOrderItemId());
        BigDecimal newTotalRefundAmount = totalRefundAmount.add(request.getAmount());
        if (newTotalRefundAmount.compareTo(paymentGroup.getPaidAmount()) > 0) {
            log.warn("累计退款金额不能大于已支付金额，跳过退款处理: orderId={}, currentRefundAmount={}, totalRefundAmount={}, paidAmount={}",
                    request.getPrentOrderId(), request.getAmount(), newTotalRefundAmount, paymentGroup.getPaidAmount());
            return;
        }
        if (request.getAmount().compareTo(paymentGroup.getPaidAmount()) > 0) {
            log.warn("单次退款金额不能大于已支付金额，跳过退款处理: orderId={}, amount={}, paidAmount={}",
                    request.getPrentOrderId(), request.getAmount(), paymentGroup.getPaidAmount());
            return;
        }
        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("退款金额必须大于0，跳过退款处理: amount={}", request.getAmount());
            return;
        }
        try {
            String refundId = randomStrUtil.generateRefundId();
            SysRefundRecord refundRecord = SysRefundRecord.create(request, paymentGroup, refundId);
            sysRefundRecordService.save(refundRecord);
            walletOperationService.increaseBalance(request.getUserId(), request.getAmount());
            sysWalletRecordService.saveRefundWalletRecord(request, paymentGroup);
            //如果是补偿订单，需要发送补偿金额到用户钱包，并保存对应的钱包记录
            if (request.getIsCompensation()) {
                if (sysWalletRecordService.isOrderItemCompensation(request.getOrderItemNo())) {
                    log.warn("一个订单只能被补偿一次: orderItemNo={}", request.getOrderItemNo());
                    return;
                }
                CompensationStrategy strategy = compensationStrategyFactory.getDefaultStrategy();
                BigDecimal compensation = strategy.calculateCompensation(request);
                walletOperationService.increaseBalance(request.getUserId(), compensation);
                sysWalletRecordService.saveCompensationWalletRecord(request, paymentGroup, compensation);
                log.info("补偿成功: userId={}, orderItemNo={}, compensation={}",
                        request.getUserId(), request.getOrderItemNo(), compensation);
            }
            log.info("退款成功: userId={},orderItemNo={}, refundId={}, 退款金额={}",
                    request.getUserId(), request.getOrderItemNo(), refundId, request.getAmount());
            //todo 发送订单取消，补偿通知邮件
        } catch (Exception e) {
            log.error("退款处理失败: userId={}, orderItemNo={}, error={}",
                    request.getUserId(), request.getOrderItemNo(), e.getMessage(), e);
            throw new ServiceException("退款处理失败: " + e.getMessage());
        }
    }
}
