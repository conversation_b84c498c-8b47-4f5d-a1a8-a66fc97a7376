package com.knet.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.resp.*;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.entity.SysOrderItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:16
 * @description: 订单聚合服务接口定义
 */
public interface ISysOrderProcessService {

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    CreateOrderResponse createOrderFromCart(CreateOrderRequest request);

    /**
     * 分页查询订单列表
     *
     * @param request 查询请求
     * @return 订单列表响应
     */
    IPage<OrderListResponse.ParentOrderResponse> queryOrderList(OrderListQueryRequest request);

    /**
     * 获取订单详细信息
     *
     * @param orderId 订单号
     * @return 订单详细信息
     */
    OrderDetailResponse getOrderDetail(String orderId);

    /**
     * 取消订单
     *
     * @param orderId 订单号
     */
    void cancelOrder(String orderId);

    /**
     * 系统取消订单（用于超时等系统自动取消场景）
     *
     * @param orderId 订单号
     */
    void systemCancelOrder(String orderId);

    /**
     * 智能更新订单状态
     * 根据物流标签分配情况，自动判断订单状态:
     * - 如果所有订单项都已分配物流标签，更新为待发货
     * - 如果部分订单项已分配物流标签，更新为部分发货
     * - 如果没有订单项分配物流标签，不更新状态
     *
     * @param parentOrderId 父订单ID
     * @return 更新结果，true表示成功，false表示失败
     */
    boolean smartUpdateOrderStatus(String parentOrderId);

    /**
     * 取消订单项
     *
     * @param itemNo 订单No
     */
    void cancelItemOrder(String itemNo);

    /**
     * 批量智能更新订单状态,根据订单子项订单列表实现
     *
     * @param request 订单
     * @return 更新结果，true表示成功，false表示失败
     */
    boolean smartUpdateOrderStatus(UpdatedOrderRequest request);

    /**
     * 支付聚合信息
     *
     * @param parentOrderId 父订单号
     * @return 支付聚合信息
     */
    PaymentDetailInfoResponse queryPaymentInfo(String parentOrderId);

    /**
     * 冻结订单项
     *
     * @param orderItems 订单项列表
     * @return 更新结果，true表示成功，false表示失败
     */
    boolean freezeOrderItem(List<SysOrderItem> orderItems);

    /**
     * 管理员分页查询所有订单列表
     *
     * @param request 管理员查询请求
     * @return 管理员订单列表响应
     */
    IPage<AdminOrderListResponse.AdminParentOrderResponse> queryAllOrderList(AdminOrderListQueryRequest request);

    /**
     * 用户主动关闭订单
     * 当订单处于待支付状态时，用户可以主动关闭订单
     * 关闭后释放锁定库存，取消延迟消息
     *
     * @param parentOrderId 父订单ID
     */
    void closeOrder(String parentOrderId);
}
